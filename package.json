{"name": "ecommerceapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node --watch ./src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "type": "module", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "cloudinary": "^2.7.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^17.2.2", "express": "^5.1.0", "express-rate-limit": "^8.1.0", "helmet": "^8.1.0", "joi": "^18.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.1", "multer": "^2.0.2", "nanoid": "^5.1.5", "nodemailer": "^7.0.6"}}